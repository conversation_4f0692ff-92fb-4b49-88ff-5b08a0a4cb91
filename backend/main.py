"""CodeOCR API server for converting code screenshots to editable code."""

import re

import instructor
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator

# --- Pydantic Models ---


class CodeOCRRequest(BaseModel):
    """Defines the structure for a request to the CodeOCR API."""

    image_source: str = Field(..., description="Base64 encoded image data or image URL")
    api_key: str = Field(..., description="API key for the vision model provider")
    api_base: str = Field("https://api.openai.com/v1", description="API base URL")
    model_name: str = Field(..., description="Model name to use for OCR")


class CodeOCRResponse(BaseModel):
    """
    Defines the structured response for the Code OCR API.
    The response contains a single 'code' field, which is a Markdown-formatted code block.
    """

    code: str = Field(
        ...,
        description=(
            "The extracted source code, formatted as a Markdown code block. "
            "It MUST start with ``` followed by the identified language, a newline, "
            "the code, and end with ```. If the language is unknown, use 'text'."
        ),
        examples=["```python\nprint('Hello, World!')\n```", "```text\nSome text\n```"],
    )

    @field_validator("code")
    @classmethod
    def validate_code_block_format(cls, v: str) -> str:
        """
        Validates that the 'code' field is a well-formed Markdown code block.
        The format must be: ```language\n...code...\n```
        """
        if not isinstance(v, str):
            raise TypeError("Input must be a string.")

        trimmed_v = v.strip()

        if not trimmed_v.startswith("```") or not trimmed_v.endswith("```"):
            raise ValueError("Code must be enclosed in triple backticks (```).")

        match = re.match(r"^```(\w+)\n(.*)\n```$", trimmed_v, re.DOTALL)

        if not match:
            raise ValueError(
                "Invalid format. Must be ```language\\n...code...\\n```. "
                "Ensure there is a language identifier after the opening backticks and a newline."
            )

        language = match.group(1)
        content = match.group(2)

        if not language:
            raise ValueError("Language identifier cannot be empty.")

        if not content.strip():
            raise ValueError("Code content inside the block cannot be empty.")

        return v


# --- Prompt Template ---

CODE_OCR_PROMPT_TEMPLATE = """
You are an expert AI assistant specializing in Optical Code Recognition (OCR).
Your task is to accurately extract source code from a given image.

**Instructions:**

1.  **Analyze the Image:** Carefully examine the user-provided image to identify any blocks of source code.
2.  **Extract the Code:** Transcribe the code text exactly as you see it, preserving original indentation, spacing, and line breaks.
3.  **Identify the Language:** Determine the programming language of the extracted code.
4.  **Format the Output:** You MUST format your response as a single Markdown code block that conforms to the required structure.

**Output Format Rules (Strictly Enforced):**

*   Your entire response MUST be a single string that will be parsed into a JSON object with a "code" field.
*   The value of the "code" field MUST start with three backticks (```).
*   Immediately following the opening backticks, you MUST specify the detected programming language (e.g., `python`, `javascript`, `java`).
*   If you are unsure of the programming language or if it's plain text, you MUST use the identifier `text`.
*   The language identifier MUST be followed by a newline character (`\n`).
*   After the newline, insert the extracted code.
*   The string MUST end with three backticks (```).

**Example of the final string value for the 'code' field:**
```python
def hello_world():
    print("Hello, World!")
```

**Final Check:** Before providing the response, double-check that it strictly adheres to the ` ```language\\n...code...\\n``` ` format. Your response will be programmatically validated.
"""

# --- FastAPI Application ---

app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(request: CodeOCRRequest):
    """
    Extracts code from an image using a vision model and returns it in a structured,
    validated format. This is a non-streaming endpoint.
    """
    try:
        # Patch the OpenAI client with instructor for structured, validated responses
        client = instructor.patch(
            AsyncOpenAI(api_key=request.api_key, base_url=request.api_base)
        )

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": CODE_OCR_PROMPT_TEMPLATE},
                    {
                        "type": "image_url",
                        "image_url": {"url": request.image_source},
                    },
                ],
            }
        ]

        # Call the API with the response_model for validation and automatic retries
        response = await client.chat.completions.create(
            model=request.model_name,
            response_model=CodeOCRResponse,
            messages=messages,
            max_retries=3,  # Automatically retry up to 3 times on validation errors
        )
        return response

    except Exception as e:
        # Log the exception for debugging purposes
        print(f"An error occurred during CodeOCR processing: {e}")
        # Raise a standard HTTP 500 error to the client
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process the image. Error: {str(e)}",
        )


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
